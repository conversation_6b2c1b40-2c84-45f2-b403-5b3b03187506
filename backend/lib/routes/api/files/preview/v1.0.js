const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(`🎬 PREVIEW REQUEST: File ID: ${fileId}`);
    console.log(`🎬 PREVIEW REQUEST: Referer: ${req.headers.referer}`);
    console.log(`🎬 PREVIEW REQUEST: User-Agent: ${req.headers['user-agent']}`);

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const userId = req.user ? req.user.id : null;

    const file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false,
      ...(userId && { ownerId: userId }) // Only show user's own files if authenticated
    }).lean();

    if (!file) {
      console.log(`🎬 PREVIEW ERROR: File not found: ${fileId}`);
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found'
      });
    }

    console.log(`🎬 PREVIEW FILE INFO:`);
    console.log(`   - Name: ${file.originalFileName}`);
    console.log(`   - MIME Type: ${file.mimeType}`);
    console.log(`   - Size: ${file.fileSize}`);
    console.log(`   - Telegram File ID: ${file.telegramFileId}`);

    // Get file URL from Telegram
    const cacheKey = cacheService.getTelegramUrlKey(file.telegramFileId);
    let fileUrl = await cacheService.get(cacheKey);

    if (!fileUrl) {
      // Get fresh URL from Telegram
      fileUrl = await telegramService.getFileUrl(file.telegramFileId);

      // Cache URL for 30 minutes (Telegram URLs expire after ~1 hour)
      try {
        await cacheService.set(cacheKey, fileUrl, 1800);
      } catch (cacheError) {
        console.warn('Cache set failed:', cacheError.message);
      }
    }

    // Set response headers for inline viewing (not download)
    res.setHeader('Content-Type', file.mimeType);
    res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(file.originalFileName)}"`);
    res.setHeader('Content-Length', file.fileSize);

    // Add comprehensive CORS headers for browser access
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Range');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Length, Content-Range, Accept-Ranges');

    // Allow iframe embedding for preview - be more permissive for Office viewers
    const isOfficeFile = file.mimeType.includes('word') ||
                        file.mimeType.includes('excel') ||
                        file.mimeType.includes('powerpoint') ||
                        file.mimeType.includes('document') ||
                        file.mimeType.includes('spreadsheet') ||
                        file.mimeType.includes('presentation') ||
                        file.mimeType.includes('officedocument');

    if (isOfficeFile) {
      // Allow embedding in Google Docs Viewer and Microsoft Office Online
      res.setHeader('X-Frame-Options', 'ALLOWALL');
      res.setHeader('Content-Security-Policy', "frame-ancestors *");
    } else {
      res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    }

    // Set appropriate CSP based on file type
    if (file.mimeType.startsWith('text/') || file.mimeType === 'application/json') {
      // For text files, allow basic styling but no scripts
      res.setHeader('Content-Security-Policy', "default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'none'; frame-ancestors 'self' http://localhost:3000 http://localhost:3001 https://docs.google.com https://view.officeapps.live.com");
    } else if (file.mimeType === 'application/pdf') {
      // For PDFs, allow necessary scripts for PDF viewer and external CDNs
      res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline'; frame-ancestors 'self' http://localhost:3000 http://localhost:3001 https://docs.google.com https://view.officeapps.live.com; worker-src 'self' blob: https://cdnjs.cloudflare.com");
      // Allow PDF.js worker and resources
      res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
      res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
    } else if (isOfficeFile) {
      // For Office files, allow embedding in external viewers
      res.setHeader('Content-Security-Policy', "frame-ancestors * https://docs.google.com https://view.officeapps.live.com");
    } else {
      // For other files, minimal restrictions
      res.setHeader('Content-Security-Policy', "frame-ancestors 'self' http://localhost:3000 http://localhost:3001 https://docs.google.com https://view.officeapps.live.com");
    }

    // Cache headers for better performance
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.setHeader('ETag', `"${file._id}-${file.uploadDate}"`);

    // Handle range requests for video/audio files
    const range = req.headers.range;
    if (range && (file.mimeType.startsWith('video/') || file.mimeType.startsWith('audio/'))) {
      res.setHeader('Accept-Ranges', 'bytes');
    }

    // Check if file type is directly previewable
    const isDirectlyPreviewable = file.mimeType.startsWith('image/') ||
                                 file.mimeType.startsWith('video/') ||
                                 file.mimeType.startsWith('audio/') ||
                                 file.mimeType === 'application/pdf' ||
                                 file.mimeType.startsWith('text/') ||
                                 file.mimeType.includes('json') ||
                                 file.mimeType.includes('xml') ||
                                 file.mimeType.includes('javascript') ||
                                 file.mimeType.includes('css');

    if (!isDirectlyPreviewable) {
      // For non-previewable files, return a 415 status to indicate unsupported media type
      return res.status(415).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File type not directly previewable via stream',
        data: {
          mimeType: file.mimeType,
          suggestion: 'Try downloading the file for preview or use external viewer'
        }
      });
    }

    try {
      // Get file stream from Telegram
      const fileStream = await telegramService.getFileStream(file.telegramFileId);

      // Set up stream error handling before piping
      fileStream.on('error', (error) => {
        console.error('Stream error:', error);
        if (!res.headersSent) {
          res.status(500).json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Failed to stream file',
            data: {
              suggestion: 'Try downloading the file instead'
            }
          });
        }
      });

      fileStream.on('end', () => {
        console.log(`File previewed: ${file.originalFileName}, ID: ${file._id}`);
      });

      // Pipe the stream to response
      fileStream.pipe(res);

    } catch (streamError) {
      console.error('Failed to get file stream:', streamError);
      return res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to get file stream',
        data: {
          suggestion: 'Try downloading the file instead'
        }
      });
    }

  } catch (error) {
    console.error('Preview error:', error);
    if (!res.headersSent) {
      res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to preview file'
      });
    }
  }
};
