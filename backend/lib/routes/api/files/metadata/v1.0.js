const CONSTANTS = require('../../../../constants');

module.exports = async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(`📊 METADATA REQUEST: File ID: ${fileId}`);

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const userId = req.user ? req.user.id : null;

    const file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false,
      ...(userId && { ownerId: userId }) // Only show user's own files if authenticated
    }).lean();

    if (!file) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found'
      });
    }

    // Enhanced metadata extraction based on file type
    const getEnhancedMetadata = (file) => {
      const mime = file.mimeType.toLowerCase();
      const metadata = {
        basicInfo: {
          name: file.originalFileName,
          size: file.size,
          mimeType: file.mimeType,
          uploadDate: file.uploadDate,
          lastModified: file.lastModified || file.uploadDate
        },
        technical: {},
        recommendations: []
      };

      // Archive files
      if (mime.includes('zip') || mime.includes('rar') || mime.includes('tar') || mime.includes('gzip') || mime.includes('7z')) {
        metadata.technical = {
          compressionType: getCompressionType(mime),
          estimatedFiles: 'Unknown (requires extraction)',
          supportedBy: ['7-Zip', 'WinRAR', 'Built-in OS extractors']
        };
        metadata.recommendations = [
          'Use 7-Zip for universal archive support',
          'Extract to view contents',
          'Scan for viruses before extraction'
        ];
      }

      // Font files
      else if (mime.includes('font') || mime.includes('ttf') || mime.includes('otf') || mime.includes('woff')) {
        metadata.technical = {
          fontFormat: getFontFormat(mime),
          webCompatible: mime.includes('woff') || mime.includes('woff2'),
          installable: mime.includes('ttf') || mime.includes('otf')
        };
        metadata.recommendations = [
          'Install to system to preview',
          'Use font management software for organization',
          'Check license before commercial use'
        ];
      }

      // 3D Model files
      else if (mime.includes('model') || mime.includes('3d') || file.originalFileName.match(/\.(obj|fbx|gltf|dae|blend|3ds|max)$/i)) {
        metadata.technical = {
          modelFormat: get3DFormat(file.originalFileName),
          viewerCompatible: isViewerCompatible(file.originalFileName),
          editableIn: getCompatibleSoftware(file.originalFileName)
        };
        metadata.recommendations = [
          'Use Blender for free 3D viewing/editing',
          'Try online 3D viewers for quick preview',
          'Check if textures are included separately'
        ];
      }

      // CAD files
      else if (mime.includes('dwg') || mime.includes('dxf') || mime.includes('step') || mime.includes('iges')) {
        metadata.technical = {
          cadFormat: getCADFormat(file.originalFileName),
          version: 'Unknown (requires specialized software)',
          compatibility: getCADCompatibility(file.originalFileName)
        };
        metadata.recommendations = [
          'Use FreeCAD for open-source viewing',
          'AutoCAD Web for online viewing',
          'Consider converting to more universal formats'
        ];
      }

      // Code files
      else if (mime.includes('javascript') || mime.includes('python') || mime.includes('java') || 
               file.originalFileName.match(/\.(js|ts|py|java|php|rb|go|rs|swift|kt|cpp|c|h)$/i)) {
        metadata.technical = {
          language: getCodeLanguage(file.originalFileName),
          syntax: 'Highlighted preview available',
          encoding: 'UTF-8 (assumed)'
        };
        metadata.recommendations = [
          'Use code editor for syntax highlighting',
          'Check encoding if special characters appear garbled',
          'Consider security scan for unknown code'
        ];
      }

      return metadata;
    };

    // Helper functions
    const getCompressionType = (mime) => {
      if (mime.includes('zip')) return 'ZIP';
      if (mime.includes('rar')) return 'RAR';
      if (mime.includes('tar')) return 'TAR';
      if (mime.includes('gzip')) return 'GZIP';
      if (mime.includes('7z')) return '7-Zip';
      return 'Unknown';
    };

    const getFontFormat = (mime) => {
      if (mime.includes('ttf')) return 'TrueType Font (TTF)';
      if (mime.includes('otf')) return 'OpenType Font (OTF)';
      if (mime.includes('woff2')) return 'Web Open Font Format 2 (WOFF2)';
      if (mime.includes('woff')) return 'Web Open Font Format (WOFF)';
      if (mime.includes('eot')) return 'Embedded OpenType (EOT)';
      return 'Unknown Font Format';
    };

    const get3DFormat = (filename) => {
      const ext = filename.split('.').pop()?.toLowerCase();
      const formats = {
        'obj': 'Wavefront OBJ',
        'fbx': 'Autodesk FBX',
        'gltf': 'GL Transmission Format',
        'glb': 'GL Transmission Format Binary',
        'dae': 'COLLADA',
        'blend': 'Blender',
        '3ds': '3D Studio',
        'max': '3ds Max',
        'maya': 'Maya',
        'c4d': 'Cinema 4D'
      };
      return formats[ext] || 'Unknown 3D Format';
    };

    const isViewerCompatible = (filename) => {
      const ext = filename.split('.').pop()?.toLowerCase();
      const webCompatible = ['obj', 'gltf', 'glb', 'dae'];
      return webCompatible.includes(ext);
    };

    const getCompatibleSoftware = (filename) => {
      const ext = filename.split('.').pop()?.toLowerCase();
      const software = {
        'obj': ['Blender', 'Maya', 'Cinema 4D', 'Online viewers'],
        'fbx': ['Blender', 'Maya', 'Unity', '3ds Max'],
        'gltf': ['Blender', 'Online viewers', 'Three.js'],
        'blend': ['Blender'],
        '3ds': ['3ds Max', 'Blender'],
        'max': ['3ds Max'],
        'maya': ['Maya', 'Blender (limited)']
      };
      return software[ext] || ['Specialized 3D software required'];
    };

    const getCADFormat = (filename) => {
      const ext = filename.split('.').pop()?.toLowerCase();
      const formats = {
        'dwg': 'AutoCAD Drawing',
        'dxf': 'Drawing Exchange Format',
        'step': 'Standard for Exchange of Product Data',
        'stp': 'Standard for Exchange of Product Data',
        'iges': 'Initial Graphics Exchange Specification',
        'igs': 'Initial Graphics Exchange Specification'
      };
      return formats[ext] || 'Unknown CAD Format';
    };

    const getCADCompatibility = (filename) => {
      const ext = filename.split('.').pop()?.toLowerCase();
      if (['dwg', 'dxf'].includes(ext)) {
        return ['AutoCAD', 'FreeCAD', 'LibreCAD', 'AutoCAD Web'];
      }
      if (['step', 'stp', 'iges', 'igs'].includes(ext)) {
        return ['FreeCAD', 'SolidWorks', 'Fusion 360', 'OnShape'];
      }
      return ['Specialized CAD software'];
    };

    const getCodeLanguage = (filename) => {
      const ext = filename.split('.').pop()?.toLowerCase();
      const languages = {
        'js': 'JavaScript',
        'ts': 'TypeScript',
        'py': 'Python',
        'java': 'Java',
        'php': 'PHP',
        'rb': 'Ruby',
        'go': 'Go',
        'rs': 'Rust',
        'swift': 'Swift',
        'kt': 'Kotlin',
        'cpp': 'C++',
        'c': 'C',
        'h': 'C/C++ Header',
        'css': 'CSS',
        'html': 'HTML',
        'xml': 'XML',
        'json': 'JSON',
        'yaml': 'YAML',
        'yml': 'YAML'
      };
      return languages[ext] || 'Unknown';
    };

    const enhancedMetadata = getEnhancedMetadata(file);

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'File metadata retrieved successfully',
      data: enhancedMetadata
    });

  } catch (error) {
    console.error('Metadata error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: 'Failed to retrieve file metadata'
    });
  }
};
