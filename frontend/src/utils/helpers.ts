export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const getFileIcon = (mimeType: string): string => {
  if (mimeType.startsWith('image/')) {
    return '🖼️';
  } else if (mimeType.startsWith('video/')) {
    return '🎥';
  } else if (mimeType.startsWith('audio/')) {
    return '🎵';
  } else if (mimeType.includes('pdf')) {
    return '📄';
  } else if (mimeType.includes('document') || mimeType.includes('word')) {
    return '📝';
  } else if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) {
    return '📊';
  } else if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) {
    return '📽️';
  } else if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('archive')) {
    return '📦';
  }
  return '📄';
};

export const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith('image/');
};

export const isVideoFile = (mimeType: string): boolean => {
  return mimeType.startsWith('video/');
};

export const isAudioFile = (mimeType: string): boolean => {
  return mimeType.startsWith('audio/');
};

export const isPdfFile = (mimeType: string): boolean => {
  return mimeType === 'application/pdf';
};

export const isTextFile = (mimeType: string): boolean => {
  return mimeType.startsWith('text/') ||
         mimeType.includes('json') ||
         mimeType.includes('javascript') ||
         mimeType.includes('css') ||
         mimeType.includes('xml') ||
         mimeType.includes('yaml') ||
         mimeType.includes('yml');
};

export const isOfficeFile = (mimeType: string): boolean => {
  return mimeType.includes('document') ||
         mimeType.includes('word') ||
         mimeType.includes('spreadsheet') ||
         mimeType.includes('excel') ||
         mimeType.includes('presentation') ||
         mimeType.includes('powerpoint') ||
         mimeType.includes('officedocument');
};

export const isArchiveFile = (mimeType: string): boolean => {
  return mimeType.includes('zip') ||
         mimeType.includes('rar') ||
         mimeType.includes('tar') ||
         mimeType.includes('gzip') ||
         mimeType.includes('7z') ||
         mimeType.includes('archive');
};

export const isCodeFile = (mimeType: string): boolean => {
  return mimeType.includes('javascript') ||
         mimeType.includes('typescript') ||
         mimeType.includes('python') ||
         mimeType.includes('java') ||
         mimeType.includes('c++') ||
         mimeType.includes('php') ||
         mimeType.includes('ruby') ||
         mimeType.includes('go') ||
         mimeType.includes('rust') ||
         mimeType.includes('swift') ||
         mimeType.includes('kotlin') ||
         mimeType === 'text/x-python' ||
         mimeType === 'text/x-java' ||
         mimeType === 'text/x-c' ||
         mimeType === 'text/x-php' ||
         mimeType === 'text/x-ruby' ||
         mimeType === 'text/x-go' ||
         mimeType === 'text/x-rust' ||
         mimeType === 'text/x-swift' ||
         mimeType === 'text/x-kotlin';
};

export const isFontFile = (mimeType: string): boolean => {
  return mimeType.includes('font') ||
         mimeType.includes('ttf') ||
         mimeType.includes('otf') ||
         mimeType.includes('woff') ||
         mimeType.includes('eot');
};

export const is3DModelFile = (mimeType: string): boolean => {
  return mimeType.includes('model') ||
         mimeType.includes('3d') ||
         mimeType.includes('obj') ||
         mimeType.includes('fbx') ||
         mimeType.includes('gltf') ||
         mimeType.includes('dae') ||
         mimeType.includes('blend') ||
         mimeType.includes('3ds') ||
         mimeType.includes('max') ||
         mimeType.includes('maya');
};

export const isCADFile = (mimeType: string): boolean => {
  return mimeType.includes('dwg') ||
         mimeType.includes('dxf') ||
         mimeType.includes('step') ||
         mimeType.includes('iges') ||
         mimeType.includes('solidworks') ||
         mimeType.includes('autocad');
};

export const getFileCategory = (mimeType: string): string => {
  if (isImageFile(mimeType)) return 'image';
  if (isVideoFile(mimeType)) return 'video';
  if (isAudioFile(mimeType)) return 'audio';
  if (isPdfFile(mimeType)) return 'pdf';
  if (isTextFile(mimeType)) return 'text';
  if (isCodeFile(mimeType)) return 'code';
  if (isOfficeFile(mimeType)) return 'office';
  if (isArchiveFile(mimeType)) return 'archive';
  if (isFontFile(mimeType)) return 'font';
  if (is3DModelFile(mimeType)) return '3d';
  if (isCADFile(mimeType)) return 'cad';
  return 'other';
};

export const truncateFileName = (fileName: string, maxLength: number = 30): string => {
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop();
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

  if (extension) {
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
    return `${truncatedName}.${extension}`;
  }

  return fileName.substring(0, maxLength - 3) + '...';
};

export const validateFileName = (fileName: string): boolean => {
  const invalidChars = /[<>:"/\\|?*]/;
  return !invalidChars.test(fileName) && fileName.trim().length > 0;
};

// Detect if user is on iOS/mobile device
export const isIOSDevice = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
         (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
};

export const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Get actual file size using ArrayBuffer (more accurate on mobile)
export const getActualFileSize = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const arrayBuffer = e.target?.result as ArrayBuffer;
      if (arrayBuffer) {
        resolve(arrayBuffer.byteLength);
      } else {
        resolve(file.size); // fallback to File.size
      }
    };
    reader.onerror = () => {
      resolve(file.size); // fallback to File.size
    };
    reader.readAsArrayBuffer(file);
  });
};

// Format file size with mobile device warning
export const formatFileSizeWithWarning = (file: File): string => {
  const basicSize = formatFileSize(file.size);

  if (isIOSDevice() || isMobileDevice()) {
    return `${basicSize} ⚠️`;
  }

  return basicSize;
};

export const downloadFile = (url: string, fileName: string): void => {
  const link = document.createElement('a');
  const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
  link.href = `${baseUrl}${url}`;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
