import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  Alert<PERSON><PERSON>le,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent,
  Grid,
  IconButton,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  Download as DownloadIcon,
  OpenInNew as OpenIcon,
  Visibility as PreviewIcon,
  Info as InfoIcon,
  Code as CodeIcon,
  Archive as ArchiveIcon,
  FontDownload as FontIcon,
  ViewInAr as Model3DIcon,
  Engineering as CADIcon,
  ContentCopy as CopyIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
} from '@mui/icons-material';
import { fileApi } from '../services/fileApi';

interface EnhancedFilePreviewProps {
  item: any;
  onLoadComplete: () => void;
}

const EnhancedFilePreview: React.FC<EnhancedFilePreviewProps> = ({ item, onLoadComplete }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<string>('');
  const [metadata, setMetadata] = useState<any>(null);
  const [previewMode, setPreviewMode] = useState<'info' | 'content' | 'external' | 'stream' | 'download-preview'>('stream');
  const [fontSize, setFontSize] = useState(14);
  const [streamError, setStreamError] = useState(false);
  const [downloadPreview, setDownloadPreview] = useState(false);

  useEffect(() => {
    loadFileInfo();
    // Try stream preview first
    tryStreamPreview();
  }, [item.id]);

  const tryStreamPreview = async () => {
    try {
      setStreamError(false);

      // Try to load the file via stream first
      const streamUrl = fileApi.previewFile(item.id);
      const response = await fetch(streamUrl);

      if (response.status === 415) {
        // File type not directly previewable via stream
        console.log('File not directly previewable via stream, trying download preview...');
        setStreamError(true);
        setPreviewMode('info');
        // Automatically try download preview for unsupported types
        setTimeout(() => handleDownloadPreview(), 1000);
        return;
      }

      if (!response.ok) {
        throw new Error(`Stream preview failed: ${response.status}`);
      }

      // Check if it's a supported preview type
      const contentType = response.headers.get('content-type') || '';

      if (contentType.startsWith('image/') ||
          contentType.startsWith('video/') ||
          contentType.startsWith('audio/') ||
          contentType === 'application/pdf' ||
          contentType.startsWith('text/') ||
          contentType.includes('json')) {
        // Stream preview successful - keep current mode
        setPreviewMode('stream');
        return;
      }

      // Not a directly previewable type, fallback to info mode
      setPreviewMode('info');

    } catch (err) {
      console.error('Stream preview failed:', err);
      setStreamError(true);
      setPreviewMode('info');
    }
  };

  const loadFileInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get enhanced file metadata
      const response = await fileApi.getFileMetadata(item.id);
      setMetadata(response.data);

      // For code/text files, try to load content
      if (response.data.technical?.syntax === 'Highlighted preview available') {
        await loadTextContent();
      }

      setLoading(false);
      onLoadComplete();
    } catch (err) {
      console.error('Error loading file info:', err);
      setError(err instanceof Error ? err.message : 'Failed to load file');
      setLoading(false);
      onLoadComplete();
    }
  };

  const loadTextContent = async () => {
    try {
      const response = await fetch(fileApi.previewFile(item.id));

      if (!response.ok) {
        throw new Error('Failed to load file content');
      }

      const text = await response.text();
      setFileContent(text);
      setPreviewMode('content');
    } catch (err) {
      console.error('Error loading text content:', err);
      // Fallback to info mode
      setPreviewMode('info');
    }
  };

  const handleDownload = () => {
    window.open(fileApi.downloadFile(item.id), '_blank');
  };

  const handleDownloadPreview = async () => {
    try {
      setDownloadPreview(true);
      setLoading(true);

      // Download file and create blob URL for preview
      const response = await fetch(fileApi.downloadFile(item.id));
      if (!response.ok) {
        throw new Error('Download failed');
      }

      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);

      // Try to preview the downloaded file
      const contentType = response.headers.get('content-type') || item.mimeType;

      if (contentType.startsWith('text/') ||
          contentType.includes('json') ||
          contentType.includes('xml') ||
          contentType.includes('javascript') ||
          contentType.includes('css')) {
        // For text files, read content
        const text = await blob.text();
        setFileContent(text);
        setPreviewMode('content');
      } else {
        // For other files, show download preview
        setPreviewMode('download-preview');
      }

      setLoading(false);
    } catch (err) {
      console.error('Download preview failed:', err);
      setError('Failed to download file for preview');
      setLoading(false);
    }
  };

  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(fileContent);
      // You might want to show a toast notification here
    } catch (err) {
      console.error('Failed to copy content:', err);
    }
  };

  const getFileTypeIcon = (category: string) => {
    switch (category) {
      case 'code': return <CodeIcon sx={{ fontSize: 48, color: '#4CAF50' }} />;
      case 'archive': return <ArchiveIcon sx={{ fontSize: 48, color: '#FF9800' }} />;
      case 'font': return <FontIcon sx={{ fontSize: 48, color: '#9C27B0' }} />;
      case '3d': return <Model3DIcon sx={{ fontSize: 48, color: '#00BCD4' }} />;
      case 'cad': return <CADIcon sx={{ fontSize: 48, color: '#FF5722' }} />;
      default: return <InfoIcon sx={{ fontSize: 48, color: '#757575' }} />;
    }
  };

  const renderStreamPreview = () => {
    const streamUrl = fileApi.previewFile(item.id);
    const mimeType = item.mimeType || '';

    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              {item.name}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {streamError && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleDownloadPreview}
                  startIcon={<DownloadIcon />}
                  disabled={downloadPreview}
                >
                  Download Preview
                </Button>
              )}
              <Button
                variant="outlined"
                size="small"
                onClick={() => setPreviewMode('info')}
              >
                File Info
              </Button>
            </Box>
          </Box>

          {streamError ? (
            <Alert severity="info" sx={{ mb: 2 }}>
              <AlertTitle>Stream Preview Not Available</AlertTitle>
              This file type cannot be previewed directly. Attempting download preview...
            </Alert>
          ) : (
            <Box
              sx={{
                width: '100%',
                height: '60vh',
                border: '1px solid',
                borderColor: 'grey.300',
                borderRadius: 1,
                overflow: 'hidden',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'grey.50',
              }}
            >
              {mimeType.startsWith('image/') ? (
                <img
                  src={streamUrl}
                  alt={item.name}
                  style={{
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain',
                  }}
                  onError={() => setStreamError(true)}
                />
              ) : mimeType.startsWith('video/') ? (
                <video
                  src={streamUrl}
                  controls
                  style={{
                    maxWidth: '100%',
                    maxHeight: '100%',
                  }}
                  onError={() => setStreamError(true)}
                >
                  Your browser does not support the video tag.
                </video>
              ) : mimeType.startsWith('audio/') ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Box sx={{ fontSize: 64, mb: 3, color: '#9C27B0' }}>🎵</Box>
                  <Typography variant="h6" gutterBottom>{item.name}</Typography>
                  <audio
                    src={streamUrl}
                    controls
                    style={{ width: '100%', maxWidth: 400 }}
                    onError={() => setStreamError(true)}
                  >
                    Your browser does not support the audio tag.
                  </audio>
                </Box>
              ) : (
                <iframe
                  src={streamUrl}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                  }}
                  title={item.name}
                  onError={() => setStreamError(true)}
                />
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderFileInfo = () => (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {getFileTypeIcon(metadata?.category)}
          <Box sx={{ ml: 2 }}>
            <Typography variant="h6" gutterBottom>
              {metadata?.basicInfo?.name || item.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {metadata?.technical?.language || metadata?.technical?.compressionType || 'File'} • {metadata?.basicInfo?.size ? `${(metadata.basicInfo.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size'}
            </Typography>
          </Box>
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2" gutterBottom>
              File Details
            </Typography>
            <List dense>
              <ListItem>
                <ListItemText
                  primary="MIME Type"
                  secondary={metadata?.basicInfo?.mimeType || item.mimeType}
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Size"
                  secondary={metadata?.basicInfo?.size ? `${(metadata.basicInfo.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Upload Date"
                  secondary={metadata?.basicInfo?.uploadDate ? new Date(metadata.basicInfo.uploadDate).toLocaleString() : 'Unknown'}
                />
              </ListItem>
              {metadata?.technical && Object.keys(metadata.technical).length > 0 && (
                <>
                  {Object.entries(metadata.technical).map(([key, value]) => (
                    <ListItem key={key}>
                      <ListItemText
                        primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                        secondary={Array.isArray(value) ? value.join(', ') : String(value)}
                      />
                    </ListItem>
                  ))}
                </>
              )}
            </List>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2" gutterBottom>
              Preview Options
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<PreviewIcon />}
                onClick={() => setPreviewMode('stream')}
                disabled={previewMode === 'stream'}
              >
                Stream Preview
              </Button>

              {metadata?.technical?.syntax === 'Highlighted preview available' && (
                <Button
                  variant="outlined"
                  startIcon={<CodeIcon />}
                  onClick={() => setPreviewMode('content')}
                  disabled={previewMode === 'content'}
                >
                  View Content
                </Button>
              )}

              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={handleDownloadPreview}
                disabled={downloadPreview}
              >
                Download Preview
              </Button>

              {metadata?.recommendations && metadata.recommendations.length > 0 && (
                <Button
                  variant="outlined"
                  startIcon={<OpenIcon />}
                  onClick={() => setPreviewMode('external')}
                >
                  External Viewer
                </Button>
              )}

              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                onClick={handleDownload}
              >
                Download File
              </Button>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderTextContent = () => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            File Content
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Zoom In">
              <IconButton onClick={() => setFontSize(prev => Math.min(prev + 2, 24))}>
                <ZoomInIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Zoom Out">
              <IconButton onClick={() => setFontSize(prev => Math.max(prev - 2, 8))}>
                <ZoomOutIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Copy Content">
              <IconButton onClick={handleCopyContent}>
                <CopyIcon />
              </IconButton>
            </Tooltip>
            <Button
              variant="outlined"
              size="small"
              onClick={() => setPreviewMode('info')}
            >
              Back to Info
            </Button>
          </Box>
        </Box>

        <Box
          sx={{
            backgroundColor: 'grey.50',
            border: '1px solid',
            borderColor: 'grey.300',
            borderRadius: 1,
            p: 2,
            maxHeight: '60vh',
            overflow: 'auto',
            fontFamily: 'monospace',
            fontSize: `${fontSize}px`,
            lineHeight: 1.5,
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
          }}
        >
          {fileContent || 'No content available'}
        </Box>
      </CardContent>
    </Card>
  );

  const renderExternalViewer = () => (
    <Card>
      <CardContent>
        <Alert severity="info" sx={{ mb: 2 }}>
          <AlertTitle>External Viewer Required</AlertTitle>
          This file type requires specialized software to view properly.
        </Alert>

        <Typography variant="h6" gutterBottom>
          Recommendations
        </Typography>

        {metadata?.recommendations && metadata.recommendations.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Tips for this file type:
            </Typography>
            <List dense>
              {metadata.recommendations.map((recommendation: string, index: number) => (
                <ListItem key={index}>
                  <ListItemText primary={recommendation} />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        <Typography variant="h6" gutterBottom>
          Recommended Software
        </Typography>

        <List>
          {metadata?.technical?.supportedBy && (
            <>
              <ListItem>
                <ListItemText
                  primary="7-Zip (Free)"
                  secondary="Universal archive manager for Windows, Mac, and Linux"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => window.open('https://www.7-zip.org/', '_blank')}
                >
                  Download
                </Button>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="WinRAR"
                  secondary="Popular archive manager with wide format support"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => window.open('https://www.win-rar.com/', '_blank')}
                >
                  Download
                </Button>
              </ListItem>
            </>
          )}

          {metadata?.category === 'font' && (
            <ListItem>
              <ListItemText
                primary="Font Viewer"
                secondary="Use your system's built-in font viewer or install the font to preview"
              />
            </ListItem>
          )}

          {metadata?.category === '3d' && (
            <>
              <ListItem>
                <ListItemText
                  primary="Blender (Free)"
                  secondary="Open-source 3D creation suite"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => window.open('https://www.blender.org/', '_blank')}
                >
                  Download
                </Button>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Online 3D Viewer"
                  secondary="View 3D models in your browser"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => window.open('https://3dviewer.net/', '_blank')}
                >
                  Open
                </Button>
              </ListItem>
            </>
          )}

          {metadata?.category === 'cad' && (
            <>
              <ListItem>
                <ListItemText
                  primary="FreeCAD (Free)"
                  secondary="Open-source CAD software"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => window.open('https://www.freecadweb.org/', '_blank')}
                >
                  Download
                </Button>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="AutoCAD Web"
                  secondary="View and edit CAD files online"
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => window.open('https://web.autocad.com/', '_blank')}
                >
                  Open
                </Button>
              </ListItem>
            </>
          )}
        </List>

        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            onClick={handleDownload}
          >
            Download File
          </Button>
          <Button
            variant="outlined"
            onClick={() => setPreviewMode('info')}
          >
            Back to Info
          </Button>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        <AlertTitle>Error</AlertTitle>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ width: '100%', maxWidth: 800, mx: 'auto' }}>
      {previewMode === 'stream' && renderStreamPreview()}
      {previewMode === 'info' && renderFileInfo()}
      {previewMode === 'content' && renderTextContent()}
      {previewMode === 'download-preview' && renderTextContent()}
      {previewMode === 'external' && renderExternalViewer()}
    </Box>
  );
};

export default EnhancedFilePreview;
